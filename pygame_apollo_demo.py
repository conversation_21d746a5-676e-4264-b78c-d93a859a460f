#!/usr/bin/env python3
"""
Apollo Demo with Pygame PS5 Controller Support

This uses pygame for reliable PS5 controller input, avoiding HID library issues.
"""

import time
import numpy as np
import mujoco

def test_pygame_controller():
    """Test pygame controller detection."""
    try:
        import pygame
        pygame.init()
        pygame.joystick.init()
        
        joystick_count = pygame.joystick.get_count()
        print(f"🎮 Found {joystick_count} game controllers")
        
        if joystick_count > 0:
            joystick = pygame.joystick.Joystick(0)
            joystick.init()
            print(f"✅ Controller: {joystick.get_name()}")
            print(f"   Axes: {joystick.get_numaxes()}")
            print(f"   Buttons: {joystick.get_numbuttons()}")
            return True, joystick
        else:
            print("❌ No controllers detected")
            return False, None
            
    except ImportError:
        print("❌ Pygame not available. Install with: pip install pygame")
        return False, None
    except Exception as e:
        print(f"❌ Controller test failed: {e}")
        return False, None

class SimpleApolloController:
    """Simple Apollo robot controller."""
    
    def __init__(self, model, data):
        self.model = model
        self.data = data
        self.target_positions = np.zeros(model.nq)
        
        # Initialize to standing pose
        self.reset_to_standing()
    
    def reset_to_standing(self):
        """Reset robot to standing position."""
        mujoco.mj_resetData(self.model, self.data)
        
        # Set basic standing pose
        if self.model.nq > 0:
            # Simple standing pose (this is very basic)
            for i in range(min(self.model.nq, 20)):
                if i < len(self.data.qpos):
                    self.data.qpos[i] = 0.0
            
            # Store as target
            self.target_positions = self.data.qpos.copy()
        
        mujoco.mj_forward(self.model, self.data)
    
    def apply_control(self, forward=0.0, strafe=0.0, turn=0.0):
        """Apply simple control commands."""
        
        if self.model.nu == 0:
            return
        
        # Very simple control - just apply forces
        # Real Apollo control would be much more sophisticated
        
        for i in range(min(self.model.nu, 20)):
            if i < len(self.data.ctrl):
                # Basic movement control
                control_value = 0.0
                
                # Simple mapping (this is very basic)
                if i < 6:  # First 6 actuators for basic movement
                    control_value = forward * 20.0
                elif i < 12:  # Next 6 for turning
                    control_value = turn * 15.0
                else:  # Remaining for stability
                    control_value = strafe * 10.0
                
                # Apply control with limits
                self.data.ctrl[i] = np.clip(control_value, -100.0, 100.0)

def run_apollo_pygame_demo():
    """Run Apollo demo with pygame controller support."""
    
    print("🚀 Apollo Robot Demo with Pygame Controller Support")
    print("=" * 60)
    
    try:
        # Test controller first
        controller_available, joystick = test_pygame_controller()
        
        # Import Apollo components
        print("\n📦 Loading Apollo components...")
        from mujoco_playground._src.locomotion.apollo import constants as apollo_constants
        from mujoco_playground._src.locomotion.apollo.base import get_assets
        
        # Load Apollo model
        print("🤖 Loading Apollo robot model...")
        assets = get_assets()
        xml_path = apollo_constants.FEET_ONLY_FLAT_TERRAIN_XML
        
        model = mujoco.MjModel.from_xml_path(xml_path, assets=assets)
        data = mujoco.MjData(model)
        
        print("✅ Apollo model loaded successfully!")
        print(f"   Bodies: {model.nbody}")
        print(f"   Joints: {model.njnt}")
        print(f"   Actuators: {model.nu}")
        
        # Initialize controller
        robot_controller = SimpleApolloController(model, data)
        
        print("\n🎮 Starting simulation...")
        if controller_available:
            print("Controls:")
            print("  Left stick: Forward/backward movement")
            print("  Right stick: Left/right turning")
            print("  Any button: Show controller status")
        else:
            print("  Running in automatic demo mode")
        print("  Press Ctrl+C to exit")
        
        # Simulation loop
        step_count = 0
        last_status_time = time.time()
        
        try:
            import pygame
            pygame.init()
            
            while True:
                # Handle pygame events
                pygame.event.pump()
                
                # Get controller input
                forward = strafe = turn = 0.0
                
                if controller_available and joystick:
                    try:
                        # Read controller axes
                        if joystick.get_numaxes() >= 4:
                            left_y = -joystick.get_axis(1)  # Forward/backward (inverted)
                            left_x = joystick.get_axis(0)   # Left/right strafe
                            right_x = joystick.get_axis(2)  # Turn left/right
                            
                            # Apply deadzone
                            deadzone = 0.1
                            if abs(left_y) > deadzone:
                                forward = left_y * 0.5
                            if abs(left_x) > deadzone:
                                strafe = left_x * 0.3
                            if abs(right_x) > deadzone:
                                turn = right_x * 0.3
                        
                        # Check for button presses
                        button_pressed = False
                        for i in range(joystick.get_numbuttons()):
                            if joystick.get_button(i):
                                button_pressed = True
                                break
                        
                        # Show status when button pressed
                        if button_pressed:
                            print(f"🎮 Controller: Forward={forward:.2f}, Strafe={strafe:.2f}, Turn={turn:.2f}")
                            
                    except Exception as e:
                        print(f"Controller read error: {e}")
                        controller_available = False
                
                else:
                    # Automatic demo movement
                    t = step_count * 0.01
                    forward = 0.1 * np.sin(t * 0.3)
                    turn = 0.05 * np.cos(t * 0.2)
                
                # Apply control to robot
                robot_controller.apply_control(forward, strafe, turn)
                
                # Step simulation
                mujoco.mj_step(model, data)
                step_count += 1
                
                # Print status occasionally
                current_time = time.time()
                if current_time - last_status_time > 5.0:  # Every 5 seconds
                    print(f"Step {step_count}: Simulation running...")
                    if model.nbody > 0:
                        # Print robot height
                        com_height = data.subtree_com[0][2] if len(data.subtree_com) > 0 else 0.0
                        print(f"  Robot height: {com_height:.3f}m")
                    last_status_time = current_time
                
                # Small delay
                time.sleep(0.01)
                
        except KeyboardInterrupt:
            print("\n⏹️  Simulation stopped by user")
        
        print("\n✅ Apollo demo completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the mujoco_playground directory")
        print("Install pygame with: pip install pygame")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    
    # Quick pygame test
    try:
        import pygame
        print("✅ Pygame available")
    except ImportError:
        print("❌ Pygame not available")
        print("Install with: pip install pygame")
        return
    
    run_apollo_pygame_demo()

if __name__ == "__main__":
    main()
