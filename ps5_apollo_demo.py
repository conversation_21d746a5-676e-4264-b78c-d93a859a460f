#!/usr/bin/env python3
"""
PS5 Controller Apollo Demo

This script runs the Apollo robot with PS5 controller support.
Fixed for proper HID library compatibility.
"""

import time
import numpy as np
import mujoco
from mujoco import viewer

# PS5 Controller Configuration
PS5_VENDOR_ID = 0x054c  # Sony
PS5_PRODUCT_ID = 0x0ce6  # DualSense Controller

class PS5Controller:
    """Simple PS5 controller reader with proper error handling."""
    
    def __init__(self):
        self.device = None
        self.connected = False
        self.last_data = None
        
    def connect(self):
        """Try to connect to PS5 controller."""
        try:
            import hid
            
            print("🎮 Searching for PS5 controller...")
            
            # Try to find PS5 controller
            devices = hid.enumerate()
            ps5_device = None
            
            for device_info in devices:
                if (device_info['vendor_id'] == PS5_VENDOR_ID and 
                    device_info['product_id'] == PS5_PRODUCT_ID):
                    ps5_device = device_info
                    break
            
            if ps5_device:
                print(f"✅ Found PS5 controller: {ps5_device.get('product_string', 'DualSense')}")
                
                self.device = hid.device()
                self.device.open(PS5_VENDOR_ID, PS5_PRODUCT_ID)
                self.device.set_nonblocking(1)
                self.connected = True
                
                print("✅ PS5 controller connected successfully!")
                return True
            else:
                print("❌ PS5 controller not found")
                print("Available devices:")
                for i, dev in enumerate(devices[:5]):
                    vendor = dev.get('vendor_id', 0)
                    product = dev.get('product_id', 0)
                    name = dev.get('product_string', 'Unknown')
                    print(f"  {i+1}. 0x{vendor:04x}:0x{product:04x} - {name}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to connect PS5 controller: {e}")
            return False
    
    def read(self):
        """Read controller input."""
        if not self.connected or not self.device:
            return None
            
        try:
            data = self.device.read(64)
            if data:
                self.last_data = data
                return self.parse_input(data)
            return self.get_default_input()
            
        except Exception as e:
            print(f"Controller read error: {e}")
            return self.get_default_input()
    
    def parse_input(self, data):
        """Parse PS5 controller input data."""
        if len(data) < 10:
            return self.get_default_input()
        
        # Basic PS5 DualSense parsing (simplified)
        # Note: This is a simplified parser - full PS5 support requires more complex parsing
        
        # Analog sticks (approximate positions)
        left_x = (data[1] - 128) / 128.0 if len(data) > 1 else 0.0
        left_y = (data[2] - 128) / 128.0 if len(data) > 2 else 0.0
        right_x = (data[3] - 128) / 128.0 if len(data) > 3 else 0.0
        right_y = (data[4] - 128) / 128.0 if len(data) > 4 else 0.0
        
        # Buttons (simplified)
        buttons = data[5] if len(data) > 5 else 0
        
        return {
            'left_stick_x': np.clip(left_x, -1.0, 1.0),
            'left_stick_y': np.clip(-left_y, -1.0, 1.0),  # Invert Y
            'right_stick_x': np.clip(right_x, -1.0, 1.0),
            'right_stick_y': np.clip(-right_y, -1.0, 1.0),  # Invert Y
            'buttons': buttons,
            'connected': True
        }
    
    def get_default_input(self):
        """Return default input when controller not available."""
        return {
            'left_stick_x': 0.0,
            'left_stick_y': 0.0,
            'right_stick_x': 0.0,
            'right_stick_y': 0.0,
            'buttons': 0,
            'connected': False
        }
    
    def close(self):
        """Close controller connection."""
        if self.device:
            try:
                self.device.close()
            except:
                pass
        self.connected = False

def run_apollo_demo():
    """Run Apollo demo with PS5 controller."""
    
    print("🚀 Apollo Robot Demo with PS5 Controller")
    print("=" * 60)
    
    try:
        # Import Apollo components
        print("📦 Loading Apollo components...")
        from mujoco_playground._src.locomotion.apollo import constants as apollo_constants
        from mujoco_playground._src.locomotion.apollo.base import get_assets
        
        # Load Apollo model
        print("🤖 Loading Apollo robot model...")
        assets = get_assets()
        xml_path = apollo_constants.FEET_ONLY_FLAT_TERRAIN_XML
        
        model = mujoco.MjModel.from_xml_path(xml_path, assets=assets)
        data = mujoco.MjData(model)
        
        print("✅ Apollo model loaded successfully!")
        print(f"   Actuators: {model.nu}")
        print(f"   Joints: {model.njnt}")
        
        # Initialize controller
        controller = PS5Controller()
        controller_connected = controller.connect()
        
        if not controller_connected:
            print("⚠️  PS5 controller not connected - using keyboard/automatic control")
            print("   Connect your PS5 controller and restart for gamepad control")
        
        # Initialize simulation
        mujoco.mj_resetData(model, data)
        
        print("\n🎮 Starting simulation...")
        print("Controls:")
        if controller_connected:
            print("  Left stick: Forward/backward, left/right movement")
            print("  Right stick: Turning")
            print("  Press any button to see controller input")
        else:
            print("  Automatic demo mode (no controller)")
        
        print("  Press Ctrl+C to exit")
        
        # Run simulation
        step_count = 0
        try:
            while True:
                # Read controller input
                if controller_connected:
                    input_data = controller.read()
                    if input_data and input_data['connected']:
                        # Use controller input for movement
                        forward = input_data['left_stick_y'] * 0.5
                        strafe = input_data['left_stick_x'] * 0.3
                        turn = input_data['right_stick_x'] * 0.3
                        
                        # Show input when buttons pressed
                        if input_data['buttons'] > 0:
                            print(f"Controller: Forward={forward:.2f}, Strafe={strafe:.2f}, Turn={turn:.2f}")
                    else:
                        forward = strafe = turn = 0.0
                else:
                    # Automatic demo movement
                    t = step_count * 0.01
                    forward = 0.2 * np.sin(t * 0.5)
                    strafe = 0.1 * np.cos(t * 0.3)
                    turn = 0.1 * np.sin(t * 0.2)
                
                # Apply simple control (this is very basic - real control would be more complex)
                if model.nu > 0:
                    # Simple proportional control
                    for i in range(min(model.nu, 10)):
                        if i < len(data.ctrl):
                            # Basic movement control (simplified)
                            data.ctrl[i] = np.clip(forward * 10 + strafe * 5 + turn * 3, -50, 50)
                
                # Step simulation
                mujoco.mj_step(model, data)
                step_count += 1
                
                # Print status occasionally
                if step_count % 1000 == 0:
                    print(f"Step {step_count}: Running simulation...")
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.001)
                
        except KeyboardInterrupt:
            print("\n⏹️  Simulation stopped by user")
        
        # Cleanup
        controller.close()
        
        print("\n✅ Apollo demo completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the mujoco_playground directory")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    
    # Check if HID library is working
    try:
        import hid
        print("✅ HID library available")
    except ImportError:
        print("❌ HID library not available")
        print("Install with: pip install hid")
        return
    
    run_apollo_demo()

if __name__ == "__main__":
    main()
