import argparse
import numpy as np
import time
import genesis as gs


def main():
    parser = argparse.ArgumentParser(description="Optimized Genesis Water Simulation Demo")
    parser.add_argument("-v", "--vis", action="store_true", default=True,
                       help="Show visualization (default: True)")
    parser.add_argument("--sim_type", choices=["sph", "pbd", "emitter", "fast_sph", "demo"], default="fast_sph",
                       help="Type of water simulation (default: fast_sph)")
    parser.add_argument("--steps", type=int, default=500,
                       help="Number of simulation steps (default: 500)")
    parser.add_argument("--particle_count", choices=["low", "medium", "high"], default="low",
                       help="Particle count preset (default: low)")
    parser.add_argument("--quality", choices=["performance", "balanced", "quality"], default="performance",
                       help="Quality vs performance preset (default: performance)")
    args = parser.parse_args()

    try:
        ########################## init ##########################
        # Use GPU backend for better performance
        gs.init(backend=gs.gpu, seed=0, precision="32", logging_level="warning")

        # Print configuration info
        particle_settings, quality_settings = get_particle_settings(args.particle_count, args.quality)
        estimated_particles = int(27000 * (0.03/particle_settings["particle_size"])**3)
        print(f"Configuration: {args.sim_type} | {args.particle_count} particles (~{estimated_particles:,}) | {args.quality} quality")

        if args.sim_type == "demo":
            run_demo()
        elif args.sim_type == "sph":
            run_sph_simulation(args)
        elif args.sim_type == "fast_sph":
            run_fast_sph_simulation(args)
        elif args.sim_type == "pbd":
            run_pbd_simulation(args)
        elif args.sim_type == "emitter":
            run_emitter_simulation(args)

    except Exception as e:
        print(f"❌ Error running simulation: {e}")
        print("Try using --particle_count low --quality performance for better performance")
        return 1

    return 0


def get_particle_settings(particle_count, quality):
    """Get optimized particle and quality settings"""
    # Optimized particle settings for better performance
    settings = {
        "low": {"particle_size": 0.03, "box_size": (0.4, 0.4, 0.3)},      # ~15K particles
        "medium": {"particle_size": 0.02, "box_size": (0.5, 0.5, 0.35)},  # ~55K particles
        "high": {"particle_size": 0.015, "box_size": (0.6, 0.6, 0.4)}     # ~125K particles
    }

    # More aggressive performance settings
    quality_settings = {
        "performance": {"dt": 10e-3, "substeps": 4, "max_fps": 120, "solver_iters": 30},
        "balanced": {"dt": 6e-3, "substeps": 6, "max_fps": 60, "solver_iters": 40},
        "quality": {"dt": 4e-3, "substeps": 10, "max_fps": 30, "solver_iters": 60}
    }

    return settings[particle_count], quality_settings[quality]


def run_fast_sph_simulation(args):
    """Run optimized SPH simulation with performance enhancements"""
    print("Running Optimized Fast SPH Water Simulation...")

    particle_settings, quality_settings = get_particle_settings(args.particle_count, args.quality)

    ########################## create a scene ##########################
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=quality_settings["dt"],
            substeps=quality_settings["substeps"],
            substeps_local=1,  # Save GPU memory
        ),
        sph_options=gs.options.SPHOptions(
            lower_bound=(-1.0, -1.0, 0.0),
            upper_bound=(1.0, 1.0, 2.0),
            particle_size=particle_settings["particle_size"],
            pressure_solver="WCSPH",  # Faster than DFSPH
            max_density_solver_iterations=quality_settings["solver_iters"],
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=False,  # Disable for performance
            show_world_frame=False,
            shadow=False,  # Disable shadows for performance
            plane_reflection=False,
            particle_size_scale=0.8,  # Smaller visual particles
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(2.5, -2.0, 1.5),
            camera_lookat=(0.0, 0.0, 0.3),
            camera_fov=45,
            max_FPS=quality_settings["max_fps"],
            res=(1280, 720),  # Optimized resolution
        ),
        show_viewer=args.vis,
    )

    ########################## entities ##########################
    # Ground plane
    scene.add_entity(morph=gs.morphs.Plane())

    # Optimized water pool
    scene.add_entity(
        material=gs.materials.SPH.Liquid(
            rho=1000.0,
            stiffness=30000.0,  # Reduced for stability
            mu=0.005,  # Lower viscosity for performance
            gamma=0.01,  # Reduced surface tension
            sampler="regular"  # Most efficient sampler
        ),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.25),
            size=particle_settings["box_size"],
        ),
        surface=gs.surfaces.Default(
            color=(0.3, 0.7, 1.0, 0.7),
            vis_mode="particle",
        ),
    )

    ########################## build ##########################
    scene.build()

    print(f"Running {args.steps} optimized simulation steps...")
    start_time = time.time()

    for i in range(args.steps):
        if i % 100 == 0:
            elapsed = time.time() - start_time
            fps = i / elapsed if elapsed > 0 else 0
            print(f"Step {i}/{args.steps} - FPS: {fps:.1f}")
        scene.step()

    total_time = time.time() - start_time
    avg_fps = args.steps / total_time
    print(f"Fast SPH simulation completed! Average FPS: {avg_fps:.1f}")


def run_sph_simulation(args):
    """Run standard SPH (Smoothed Particle Hydrodynamics) water simulation"""
    print("Running Standard SPH Water Simulation...")

    ########################## create a scene ##########################
    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=4e-3,
            substeps=10,
        ),
        sph_options=gs.options.SPHOptions(
            lower_bound=(-1.0, -1.0, 0.0),
            upper_bound=(1.0, 1.0, 2.0),
            particle_size=0.01,
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=True,
            show_world_frame=True,
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(3.5, -2.5, 2.0),
            camera_lookat=(0.0, 0.0, 0.5),
            camera_fov=40,
            max_FPS=60,
        ),
        show_viewer=args.vis,
    )

    ########################## entities ##########################
    # Ground plane
    plane = scene.add_entity(
        morph=gs.morphs.Plane(),
    )

    # Water pool
    water = scene.add_entity(
        material=gs.materials.SPH.Liquid(
            rho=1000.0,      # density
            mu=0.01,         # viscosity
            gamma=0.02,      # surface tension
            sampler="regular"
        ),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.3),
            size=(0.8, 0.8, 0.6),
        ),
        surface=gs.surfaces.Default(
            color=(0.2, 0.6, 1.0, 0.8),
            vis_mode="particle",
        ),
    )

    # Optional: Add a cube to interact with water
    cube = scene.add_entity(
        material=gs.materials.Rigid(needs_coup=True, coup_friction=0.1),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 1.5),
            size=(0.15, 0.15, 0.15),
            euler=(45, 30, 0),
            fixed=False,
        ),
        surface=gs.surfaces.Default(
            color=(0.8, 0.2, 0.2, 1.0),
        ),
    )

    ########################## build ##########################
    scene.build()

    print(f"Running {args.steps} simulation steps...")
    for i in range(args.steps):
        if i % 100 == 0:
            print(f"Step {i}/{args.steps}")
        scene.step()

    print("SPH simulation completed!")


def run_pbd_simulation(args):
    """Run optimized PBD (Position Based Dynamics) water simulation"""
    print("Running Optimized PBD Water Simulation...")

    particle_settings, quality_settings = get_particle_settings(args.particle_count, args.quality)

    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=quality_settings["dt"],
            substeps=quality_settings["substeps"],
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(2.5, 1.0, 2.0),
            camera_lookat=(0.5, 0.5, 0.3),
            camera_fov=45,
            max_FPS=quality_settings["max_fps"],
        ),
        vis_options=gs.options.VisOptions(
            shadow=False,
            plane_reflection=False,
            particle_size_scale=0.9,
        ),
        show_viewer=args.vis,
        pbd_options=gs.options.PBDOptions(
            lower_bound=(0.0, 0.0, 0.0),
            upper_bound=(1.0, 1.0, 1.0),
            max_density_solver_iterations=max(3, quality_settings["solver_iters"] // 10),
            max_viscosity_solver_iterations=1,
            particle_size=particle_settings["particle_size"],
        ),
    )

    ########################## entities ##########################
    scene.add_entity(
        material=gs.materials.PBD.Liquid(
            rho=1000.0,
            density_relaxation=0.8,  # Slightly reduced for stability
            viscosity_relaxation=0.0,
            sampler="regular"
        ),
        morph=gs.morphs.Box(
            lower=(0.2, 0.2, 0.1),
            upper=(0.8, 0.8, 0.5)
        ),
        surface=gs.surfaces.Default(
            color=(0.3, 0.7, 1.0, 0.8),
            vis_mode="particle",
        ),
    )

    scene.build()

    print(f"Running {args.steps} optimized PBD simulation steps...")
    start_time = time.time()

    for i in range(args.steps):
        if i % 100 == 0:
            elapsed = time.time() - start_time
            fps = i / elapsed if elapsed > 0 else 0
            print(f"Step {i}/{args.steps} - FPS: {fps:.1f}")
        scene.step()

    total_time = time.time() - start_time
    avg_fps = args.steps / total_time
    print(f"PBD simulation completed! Average FPS: {avg_fps:.1f}")


def run_emitter_simulation(args):
    """Run optimized water emitter simulation with continuous water drops"""
    print("Running Optimized Water Emitter Simulation...")

    particle_settings, quality_settings = get_particle_settings(args.particle_count, args.quality)

    scene = gs.Scene(
        sim_options=gs.options.SimOptions(
            dt=quality_settings["dt"],
            substeps=quality_settings["substeps"],
        ),
        sph_options=gs.options.SPHOptions(
            particle_size=particle_settings["particle_size"],
            lower_bound=(-1.0, -1.0, 0.0),
            upper_bound=(1.0, 1.0, 3.0),
            pressure_solver="WCSPH",
        ),
        viewer_options=gs.options.ViewerOptions(
            camera_pos=(2.5, -2.0, 1.8),
            camera_lookat=(0.0, 0.0, 0.8),
            camera_fov=45,
            max_FPS=quality_settings["max_fps"],
        ),
        vis_options=gs.options.VisOptions(
            visualize_sph_boundary=False,
            shadow=False,
            plane_reflection=False,
            particle_size_scale=0.8,
        ),
        show_viewer=args.vis,
    )

    # Ground plane
    scene.add_entity(gs.morphs.Plane())

    # Water container/pool at bottom
    scene.add_entity(
        material=gs.materials.SPH.Liquid(
            sampler="regular",
            mu=0.005,
            gamma=0.01
        ),
        morph=gs.morphs.Box(
            pos=(0.0, 0.0, 0.08),
            size=(0.8, 0.8, 0.16),
        ),
        surface=gs.surfaces.Default(
            color=(0.2, 0.6, 1.0, 0.7),
            vis_mode="particle",
        ),
    )

    # Water emitter
    emitter = scene.add_emitter(
        material=gs.materials.SPH.Liquid(
            sampler="regular",
            mu=0.005,
            gamma=0.01
        ),
        max_particles=30000,  # Reduced for performance
        surface=gs.surfaces.Default(
            color=(0.4, 0.8, 1.0, 0.8),
            vis_mode="particle",
        ),
    )

    scene.build()

    print(f"Running {args.steps} optimized emitter simulation steps...")
    start_time = time.time()

    for i in range(args.steps):
        if i % 50 == 0:
            elapsed = time.time() - start_time
            fps = i / elapsed if elapsed > 0 else 0
            print(f"Step {i}/{args.steps} - FPS: {fps:.1f}")

        # Emit water drops periodically
        if i % 15 == 0:  # Emit every 15 steps for performance
            emitter.emit(
                pos=np.array([0.0, 0.0, 2.2]),
                direction=np.array([0.0, 0.0, -1.0]),
                speed=2.5,
                droplet_shape="circle",
                droplet_size=0.12,
            )

        scene.step()

    total_time = time.time() - start_time
    avg_fps = args.steps / total_time
    print(f"Emitter simulation completed! Average FPS: {avg_fps:.1f}")


def run_demo():
    """Run a quick demo of all simulation types"""
    print("Running Quick Demo of All Simulation Types...")

    demo_args = type('Args', (), {
        'vis': True,
        'steps': 200,
        'particle_count': 'low',
        'quality': 'performance'
    })()

    print("\n=== Fast SPH Demo ===")
    run_fast_sph_simulation(demo_args)

    print("\n=== PBD Demo ===")
    run_pbd_simulation(demo_args)

    print("\n=== Emitter Demo ===")
    run_emitter_simulation(demo_args)

    print("\nDemo completed!")


if __name__ == "__main__":
    main()
