#!/usr/bin/env python3
"""
Test MuJoCo Playground installation and check for Apollo environments.
"""

import sys
import traceback

def test_basic_import():
    """Test basic MuJoCo Playground import."""
    print("🔍 Testing MuJoCo Playground import...")
    try:
        import mujoco_playground
        print("✅ MuJoCo Playground imported successfully!")
        
        # Check version if available
        if hasattr(mujoco_playground, '__version__'):
            print(f"Version: {mujoco_playground.__version__}")
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import MuJoCo Playground: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

def test_registry():
    """Test environment registry."""
    print("\n🔍 Testing environment registry...")
    try:
        from mujoco_playground import registry
        print("✅ Registry imported successfully!")
        
        # List all environments
        all_envs = registry.ALL_ENVS
        print(f"Total environments available: {len(all_envs)}")
        
        # Look for Apollo environments
        apollo_envs = [env for env in all_envs if 'apollo' in env.lower()]
        print(f"Apollo environments found: {apollo_envs}")
        
        # Look for locomotion environments
        locomotion_envs = [env for env in all_envs if any(keyword in env.lower() for keyword in ['walk', 'run', 'locomotion', 'getup', 'go1', 'go2', 'spot'])]
        print(f"Locomotion environments found: {locomotion_envs[:10]}")  # Show first 10
        
        return apollo_envs, locomotion_envs
    except ImportError as e:
        print(f"❌ Failed to import registry: {e}")
        return [], []
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()
        return [], []

def test_apollo_import():
    """Test Apollo-specific imports."""
    print("\n🔍 Testing Apollo-specific imports...")
    try:
        # Try different Apollo import paths
        import_paths = [
            "mujoco_playground.locomotion.apollo",
            "mujoco_playground._src.locomotion.apollo",
            "mujoco_playground.locomotion.apollo.constants",
            "mujoco_playground._src.locomotion.apollo.constants"
        ]
        
        for path in import_paths:
            try:
                module = __import__(path, fromlist=[''])
                print(f"✅ Successfully imported: {path}")
                
                # Check for constants
                if hasattr(module, 'constants'):
                    constants = module.constants
                    if hasattr(constants, 'FEET_ONLY_FLAT_TERRAIN_XML'):
                        print(f"   Apollo XML path: {constants.FEET_ONLY_FLAT_TERRAIN_XML}")
                
                return True
            except ImportError:
                print(f"❌ Failed to import: {path}")
                continue
        
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

def test_jax_backend():
    """Test JAX backend."""
    print("\n🔍 Testing JAX backend...")
    try:
        import jax
        print(f"✅ JAX imported successfully!")
        print(f"JAX backend: {jax.default_backend()}")
        print(f"JAX devices: {jax.devices()}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import JAX: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 MuJoCo Playground Installation Test")
    print("=" * 50)
    
    # Test basic import
    if not test_basic_import():
        print("\n❌ Basic import failed. Please install MuJoCo Playground:")
        print("   pip install playground")
        return False
    
    # Test JAX
    test_jax_backend()
    
    # Test registry
    apollo_envs, locomotion_envs = test_registry()
    
    # Test Apollo imports
    apollo_available = test_apollo_import()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"✅ MuJoCo Playground: Installed")
    print(f"{'✅' if apollo_envs else '❌'} Apollo environments: {len(apollo_envs)} found")
    print(f"✅ Locomotion environments: {len(locomotion_envs)} found")
    print(f"{'✅' if apollo_available else '❌'} Apollo module: {'Available' if apollo_available else 'Not found'}")
    
    if apollo_envs:
        print(f"\n🤖 Available Apollo environments:")
        for env in apollo_envs:
            print(f"   - {env}")
    elif locomotion_envs:
        print(f"\n🤖 Available locomotion environments (first 5):")
        for env in locomotion_envs[:5]:
            print(f"   - {env}")
    
    return True

if __name__ == "__main__":
    main()
