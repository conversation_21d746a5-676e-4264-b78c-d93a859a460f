#!/usr/bin/env python3
"""
MuJoCo Playground Getup Demo

This script demonstrates the "getup" environments where robots learn to get up from lying positions.
Available getup environments:
- Go1Getup: Quadruped robot (Go1) getting up
- SpotGetup: Boston Dynamics Spot robot getting up
"""

import jax
import jax.numpy as jnp
import time
import argparse

try:
    import mujoco_playground
    from mujoco_playground import registry
    print("✅ MuJoCo Playground imported successfully!")
except ImportError as e:
    print(f"❌ Failed to import MuJoCo Playground: {e}")
    print("Please install with: pip install playground")
    exit(1)


def demo_getup_environment(env_name: str, num_steps: int = 100, num_episodes: int = 1):
    """Demo a getup environment."""
    print(f"\n{'='*60}")
    print(f"🤖 GETUP DEMO: {env_name}")
    print(f"{'='*60}")
    
    try:
        # Load environment
        print(f"Loading environment: {env_name}")
        env_cfg = registry.get_default_config(env_name)
        env = registry.load(env_name, config=env_cfg)
        
        print(f"Environment loaded successfully!")
        print(f"Observation space: {env.observation_size}")
        print(f"Action space: {env.action_size}")
        print(f"Episode length: {env_cfg.episode_length}")
        
        # Run episodes
        rng = jax.random.PRNGKey(42)
        
        for episode in range(num_episodes):
            print(f"\n🎬 Episode {episode + 1}/{num_episodes}")
            print("-" * 40)
            
            # Reset environment
            rng, reset_rng = jax.random.split(rng)
            state = env.reset(reset_rng)
            
            print(f"Environment reset. Initial state shape: {state.obs.shape}")
            
            total_reward = 0.0
            step_count = 0
            
            # Run episode
            for step in range(num_steps):
                # Generate random action (in a real scenario, this would be from a trained policy)
                rng, action_rng = jax.random.split(rng)
                action = jax.random.uniform(
                    action_rng, 
                    (env.action_size,), 
                    minval=-1.0, 
                    maxval=1.0
                )
                
                # Step environment
                state = env.step(state, action)
                total_reward += float(state.reward)
                step_count += 1
                
                # Print progress every 20 steps
                if step % 20 == 0:
                    print(f"Step {step:3d}: reward={state.reward:.3f}, total_reward={total_reward:.3f}, done={state.done}")
                
                # Check if episode is done
                if state.done:
                    print(f"Episode finished at step {step}!")
                    break
            
            print(f"Episode {episode + 1} completed:")
            print(f"  Total steps: {step_count}")
            print(f"  Total reward: {total_reward:.3f}")
            print(f"  Average reward: {total_reward/step_count:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error running {env_name}: {e}")
        import traceback
        traceback.print_exc()
        return False


def list_getup_environments():
    """List all available getup environments."""
    print(f"\n{'='*60}")
    print(f"🤖 AVAILABLE GETUP ENVIRONMENTS")
    print(f"{'='*60}")
    
    try:
        all_envs = registry.ALL_ENVS
        getup_envs = [env for env in all_envs if 'getup' in env.lower()]
        
        if getup_envs:
            print(f"Found {len(getup_envs)} getup environments:")
            for env in getup_envs:
                print(f"  - {env}")
        else:
            print("No getup environments found.")
            print("Available locomotion environments:")
            locomotion_envs = [env for env in all_envs if env in mujoco_playground.locomotion.ALL_ENVS]
            for env in locomotion_envs[:10]:  # Show first 10
                print(f"  - {env}")
                
    except Exception as e:
        print(f"❌ Error listing environments: {e}")


def main():
    """Main function to run getup demos."""
    parser = argparse.ArgumentParser(description="MuJoCo Playground Getup Demo")
    parser.add_argument("--env", type=str, default="Go1Getup", 
                       choices=["Go1Getup", "SpotGetup", "both"],
                       help="Environment to demo (default: Go1Getup)")
    parser.add_argument("--steps", type=int, default=100,
                       help="Number of steps per episode (default: 100)")
    parser.add_argument("--episodes", type=int, default=1,
                       help="Number of episodes to run (default: 1)")
    parser.add_argument("--list", action="store_true",
                       help="List available getup environments")
    
    args = parser.parse_args()
    
    print("🤖 MuJoCo Playground Getup Demo")
    print("This demo shows robots learning to get up from lying positions.")
    
    # Check JAX setup
    print(f"\n🔧 System Info:")
    print(f"JAX version: {jax.__version__}")
    print(f"JAX devices: {jax.devices()}")
    print(f"JAX default backend: {jax.default_backend()}")
    
    if args.list:
        list_getup_environments()
        return
    
    # Run demos
    success_count = 0
    
    if args.env == "both":
        envs_to_demo = ["Go1Getup", "SpotGetup"]
    else:
        envs_to_demo = [args.env]
    
    for env_name in envs_to_demo:
        if demo_getup_environment(env_name, args.steps, args.episodes):
            success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🎉 DEMO SUMMARY")
    print(f"{'='*60}")
    print(f"Successful demos: {success_count}/{len(envs_to_demo)}")
    
    if success_count > 0:
        print("✅ Getup environments are working!")
        print("\nKey features demonstrated:")
        print("  🤖 Robot getup behavior simulation")
        print("  🎯 Environment loading and configuration")
        print("  🔄 Episode reset and step functionality")
        print("  📊 Reward tracking")
        print("  🎮 Random policy rollouts")
        
        print(f"\nTo train a real policy:")
        print(f"  python3 learning/train_jax_ppo.py --env_name={envs_to_demo[0]}")
    else:
        print("❌ Some issues encountered. Check the error messages above.")


if __name__ == '__main__':
    main()
