#!/usr/bin/env python3
"""
Apollo Robot Demo for MuJoCo Playground

This script demonstrates how to run an Apollo robot simulation.
Apollo is a humanoid robot environment in MuJoCo Playground.
"""

import sys
import os
import time

def print_banner():
    print("🚀 Apollo Robot Demo - MuJoCo Playground")
    print("=" * 60)
    print("This demo will run an Apollo humanoid robot simulation.")
    print("Apollo is a humanoid robot that can perform locomotion tasks.")
    print("=" * 60)

def test_basic_imports():
    """Test basic imports step by step."""
    print("🔍 Testing basic imports...")
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except Exception as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        print("   Importing JAX (this may take a moment)...")
        import jax
        print("✅ JAX imported successfully")
        try:
            backend = jax.default_backend()
            print(f"   JAX backend: {backend}")
        except Exception as e:
            print(f"   JAX backend check failed: {e}")
    except Exception as e:
        print(f"❌ JAX import failed: {e}")
        print("   This might be due to missing dependencies or GPU issues.")
        return False
    
    return True

def test_mujoco_playground():
    """Test MuJoCo Playground import and find Apollo environments."""
    print("\n🔍 Testing MuJoCo Playground...")
    
    try:
        print("   Importing mujoco_playground (this may take a moment)...")
        import mujoco_playground
        print("✅ MuJoCo Playground imported successfully")
        
        print("   Importing registry...")
        from mujoco_playground import registry
        print("✅ Registry imported successfully")
        
        print("   Getting environment list...")
        all_envs = registry.ALL_ENVS
        print(f"✅ Found {len(all_envs)} total environments")
        
        # Look for Apollo environments
        apollo_envs = [env for env in all_envs if 'apollo' in env.lower()]
        print(f"   Apollo environments: {apollo_envs}")
        
        # Look for humanoid/locomotion environments
        locomotion_keywords = ['humanoid', 'walk', 'getup', 'go1', 'go2', 'spot', 'locomotion', 'h1', 'g1']
        locomotion_envs = [env for env in all_envs if any(keyword in env.lower() for keyword in locomotion_keywords)]
        print(f"   Locomotion environments found: {len(locomotion_envs)}")
        if locomotion_envs:
            print(f"   First 5 locomotion environments: {locomotion_envs[:5]}")
        
        return apollo_envs, locomotion_envs
        
    except Exception as e:
        print(f"❌ MuJoCo Playground test failed: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def run_apollo_demo(env_name):
    """Run the Apollo demo with the given environment."""
    print(f"\n🤖 Running Apollo demo with environment: {env_name}")
    print("=" * 60)
    
    try:
        import jax
        import jax.numpy as jnp
        from mujoco_playground import registry
        
        print("   Creating environment...")
        env = registry.make(env_name)
        print("✅ Environment created successfully!")
        
        print("   Environment details:")
        print(f"     Action size: {env.action_size}")
        print(f"     Observation size: {env.observation_size}")
        
        print("   Resetting environment...")
        key = jax.random.PRNGKey(42)
        state = env.reset(key)
        print("✅ Environment reset successfully!")
        
        print("   Running simulation steps...")
        num_steps = 100
        for step in range(num_steps):
            # Use zero actions (let physics take over - robot will fall naturally)
            action = jnp.zeros(env.action_size)
            state = env.step(state, action)
            
            if step % 20 == 0:
                print(f"     Step {step}/{num_steps}")
        
        print("✅ Simulation completed successfully!")
        print(f"\n🎉 Apollo demo with {env_name} finished!")
        print("The robot simulation ran for 100 steps with zero actions.")
        print("This demonstrates the basic physics simulation working correctly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print_banner()
    
    # Test basic imports
    print("Step 1: Testing basic imports...")
    if not test_basic_imports():
        print("\n❌ Basic imports failed. Please check your Python environment.")
        print("Make sure JAX and NumPy are properly installed.")
        return
    
    # Test MuJoCo Playground
    print("\nStep 2: Testing MuJoCo Playground...")
    apollo_envs, locomotion_envs = test_mujoco_playground()
    
    if not apollo_envs and not locomotion_envs:
        print("\n❌ No suitable environments found.")
        print("Please ensure MuJoCo Playground is properly installed.")
        return
    
    # Choose environment to run
    if apollo_envs:
        chosen_env = apollo_envs[0]
        print(f"\n🎯 Found Apollo environment: {chosen_env}")
    elif locomotion_envs:
        chosen_env = locomotion_envs[0]
        print(f"\n🎯 Using locomotion environment: {chosen_env}")
    else:
        print("\n❌ No suitable environments available.")
        return
    
    # Run the demo
    print(f"\nStep 3: Running demo...")
    success = run_apollo_demo(chosen_env)
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 APOLLO DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("✅ MuJoCo Playground is working correctly")
        print("✅ Apollo/locomotion environment loaded successfully")
        print("✅ Physics simulation ran for 100 steps")
        print("✅ All systems operational!")
        print("\nYou can now use MuJoCo Playground for robot simulations!")
    else:
        print("\n" + "=" * 60)
        print("❌ DEMO FAILED")
        print("=" * 60)
        print("Please check the error messages above for troubleshooting.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
