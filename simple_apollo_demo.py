#!/usr/bin/env python3
"""
Simple Apollo Demo - No Joystick Required

This demonstrates the Apollo humanoid robot using basic MuJoCo simulation
without requiring joystick/gamepad dependencies.
"""

import os
import sys
import numpy as np

def print_banner():
    print("🚀 Simple Apollo Demo - MuJoCo Playground")
    print("=" * 60)
    print("Running Apollo humanoid robot simulation")
    print("No joystick/gamepad required!")
    print("=" * 60)

def run_apollo_demo():
    """Run Apollo demo using basic MuJoCo simulation."""
    
    try:
        print("📦 Importing MuJoCo...")
        import mujoco
        print("✅ MuJoCo imported successfully")
        
        print("📦 Importing Apollo components...")
        from mujoco_playground._src.locomotion.apollo import constants as apollo_constants
        from mujoco_playground._src.locomotion.apollo.base import get_assets
        print("✅ Apollo components imported successfully")
        
        print("🤖 Loading Apollo robot model...")
        
        # Get Apollo assets and XML path
        assets = get_assets()
        xml_path = apollo_constants.FEET_ONLY_FLAT_TERRAIN_XML
        
        print(f"   XML path: {xml_path}")
        
        # Create MuJoCo model and data
        model = mujoco.MjModel.from_xml_path(xml_path, assets=assets)
        data = mujoco.MjData(model)
        
        print("✅ Apollo model loaded successfully!")
        print(f"   Number of bodies: {model.nbody}")
        print(f"   Number of joints: {model.njnt}")
        print(f"   Number of actuators: {model.nu}")
        print(f"   Number of degrees of freedom: {model.nv}")
        
        # Set initial pose (standing position)
        print("🏃 Setting initial standing pose...")
        
        # Reset to default position
        mujoco.mj_resetData(model, data)
        
        # Set some reasonable joint angles for standing
        if model.njnt > 0:
            # Set hip, knee, and ankle angles for a standing pose
            joint_names = []
            for i in range(model.njnt):
                joint_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_JOINT, i)
                if joint_name:
                    joint_names.append(joint_name)
            
            print(f"   Found {len(joint_names)} joints")
            
            # Set basic standing pose
            for i, joint_name in enumerate(joint_names[:min(len(joint_names), 10)]):
                if joint_name and i < model.nq:
                    if 'hip' in joint_name.lower():
                        data.qpos[i] = 0.0  # Neutral hip
                    elif 'knee' in joint_name.lower():
                        data.qpos[i] = -0.1  # Slightly bent knee
                    elif 'ankle' in joint_name.lower():
                        data.qpos[i] = 0.05  # Slight forward lean
        
        # Forward kinematics to update positions
        mujoco.mj_forward(model, data)
        
        print("🎮 Running physics simulation...")
        
        # Run simulation for several steps
        num_steps = 500
        for step in range(num_steps):
            
            # Apply simple PD control to maintain standing pose
            if model.nu > 0:
                # Simple proportional control to maintain joint positions
                kp = 100.0  # Proportional gain
                kd = 10.0   # Derivative gain
                
                for i in range(min(model.nu, model.nv)):
                    if i < len(data.qpos) and i < len(data.qvel):
                        # Target position (current position)
                        target_pos = data.qpos[i] if step == 0 else target_positions[i]
                        
                        # PD control
                        pos_error = target_pos - data.qpos[i]
                        vel_error = 0.0 - data.qvel[i]
                        
                        data.ctrl[i] = kp * pos_error + kd * vel_error
                        
                        # Limit control forces
                        data.ctrl[i] = np.clip(data.ctrl[i], -100.0, 100.0)
            
            # Store target positions on first step
            if step == 0:
                target_positions = data.qpos.copy()
            
            # Step the simulation
            mujoco.mj_step(model, data)
            
            # Print progress
            if step % 100 == 0:
                print(f"   Step {step}/{num_steps}")
                if model.nbody > 0:
                    # Print center of mass height
                    com_height = data.subtree_com[0][2] if len(data.subtree_com) > 0 else 0.0
                    print(f"     Center of mass height: {com_height:.3f}m")
        
        print("✅ Simulation completed successfully!")
        
        # Print final state
        print("\n📊 Final simulation state:")
        if model.nbody > 0 and len(data.subtree_com) > 0:
            com = data.subtree_com[0]
            print(f"   Final center of mass: ({com[0]:.3f}, {com[1]:.3f}, {com[2]:.3f})")
        
        print("\n🎉 APOLLO DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("✅ Apollo humanoid robot loaded and simulated")
        print("✅ Physics simulation ran for 500 steps")
        print("✅ Basic standing control demonstrated")
        print("✅ MuJoCo Playground is working correctly!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you're in the mujoco_playground directory")
        print("2. Verify MuJoCo Playground is installed: pip install -e '.[all]'")
        return False
        
    except Exception as e:
        print(f"❌ Error during simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print_banner()
    
    # Check if we're in the right directory
    if not os.path.exists("mujoco_playground"):
        print("❌ Error: mujoco_playground directory not found")
        print("Please run this script from the mujoco_playground root directory")
        return
    
    success = run_apollo_demo()
    
    if success:
        print("\n🎯 Next Steps:")
        print("1. Explore other robot environments in mujoco_playground/_src/locomotion/")
        print("2. Check out the Jupyter notebooks in learning/notebooks/")
        print("3. Try the joystick demos after installing HID libraries")
        print("4. Experiment with different control algorithms")
    else:
        print("\n🔧 If you encounter issues:")
        print("1. Ensure you're in the correct directory")
        print("2. Check that all dependencies are installed")
        print("3. Try running: python -c 'import mujoco; print(\"MuJoCo OK\")'")

if __name__ == "__main__":
    main()
