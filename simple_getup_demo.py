#!/usr/bin/env python3
"""
Simple MuJoCo Playground Getup Demo

A minimal script to demonstrate the getup environments.
"""

import os
import sys

# Set environment variables for better performance
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
# Use osmesa for headless rendering on macOS, or remove MUJOCO_GL to use default
if sys.platform == "darwin":  # macOS
    # Don't set MUJOCO_GL on macOS, use default
    pass
else:
    os.environ["MUJOCO_GL"] = "egl"

print("🤖 Simple MuJoCo Playground Getup Demo")
print("=" * 50)

# Test basic imports first
print("Testing imports...")
try:
    import jax
    print(f"✅ JAX {jax.__version__} imported")
    print(f"   Devices: {jax.devices()}")
    print(f"   Backend: {jax.default_backend()}")
except ImportError as e:
    print(f"❌ JAX import failed: {e}")
    sys.exit(1)

try:
    import mujoco_playground
    print("✅ MuJoCo Playground imported")
except ImportError as e:
    print(f"❌ MuJoCo Playground import failed: {e}")
    print("Please install with: pip install playground")
    sys.exit(1)

try:
    from mujoco_playground import registry
    print("✅ Registry imported")
except ImportError as e:
    print(f"❌ Registry import failed: {e}")
    sys.exit(1)

# List available environments
print("\nListing available environments...")
try:
    all_envs = registry.ALL_ENVS
    print(f"Total environments: {len(all_envs)}")
    
    # Find getup environments
    getup_envs = [env for env in all_envs if 'getup' in env.lower()]
    print(f"Getup environments found: {getup_envs}")
    
    if not getup_envs:
        print("No getup environments found. Available locomotion environments:")
        import mujoco_playground.locomotion
        for env in mujoco_playground.locomotion.ALL_ENVS[:5]:
            print(f"  - {env}")
        sys.exit(1)
        
except Exception as e:
    print(f"❌ Error listing environments: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Try to load and run a simple demo
env_name = getup_envs[0]  # Use first getup environment
print(f"\n🚀 Testing {env_name} environment...")

try:
    # Load environment config
    print("Loading environment config...")
    env_cfg = registry.get_default_config(env_name)
    print(f"✅ Config loaded: episode_length={env_cfg.episode_length}")
    
    # Load environment
    print("Loading environment...")
    env = registry.load(env_name, config=env_cfg)
    print(f"✅ Environment loaded")
    print(f"   Observation size: {env.observation_size}")
    print(f"   Action size: {env.action_size}")
    
    # Reset environment
    print("Resetting environment...")
    rng = jax.random.PRNGKey(42)
    state = env.reset(rng)
    print(f"✅ Environment reset")
    print(f"   State shape: {state.obs.shape}")
    print(f"   Initial reward: {state.reward}")
    
    # Run a few steps
    print("Running 5 simulation steps...")
    for i in range(5):
        # Random action
        rng, action_rng = jax.random.split(rng)
        action = jax.random.uniform(action_rng, (env.action_size,), minval=-0.1, maxval=0.1)
        
        # Step
        state = env.step(state, action)
        print(f"  Step {i+1}: reward={state.reward:.4f}, done={state.done}")
        
        if state.done:
            print("  Episode finished!")
            break
    
    print(f"\n✅ {env_name} demo completed successfully!")
    print("\nTo run full training:")
    print(f"  python3 learning/train_jax_ppo.py --env_name={env_name} --num_timesteps=50000")
    
except Exception as e:
    print(f"❌ Error running {env_name}: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Demo completed successfully!")
